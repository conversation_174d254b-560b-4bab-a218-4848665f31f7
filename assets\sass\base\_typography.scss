html,
body {
	scroll-behavior: auto !important;
}

body {
	font-family: $body-font;
	font-size: $body-font-size;
	font-weight: $body-font-weight;
	color: $body-color;
	line-height: $body-line-Height;
	overflow-x: hidden;
	background: $body-background;
	-webkit-font-smoothing: antialiased;
	/***scroll-bar***/
    &::-webkit-scrollbar {
        width: 10px;
        height: 10px;
		border-radius: 0px;
    }
    &::-webkit-scrollbar-track {
        background: rgba(252,0, 18, 0.1);
		box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    	-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
		border-radius: 0px;
    }
    &::-webkit-scrollbar-thumb {
		background-color: $theme-color;
		background-image: -webkit-linear-gradient(
			45deg,
			rgba(255, 255, 255, 0.3) 25%,
			transparent 20%,
			transparent 50%,
			rgba(255, 255, 255, 0.3) 50%,
			rgba(255, 255, 255, 0.3) 75%,
			transparent 75%,
			transparent
		);
		border-radius: 0px;
    }
}
.theme-yellow {
	--theme-color: #FFEC40;
	--theme-color2: #FF4F38;
	--th-body-background: #13182B;
	.color-scheme-wrap .switchIcon {
		color: $black-color2;
	}
	.preloader .th-btn {
		color: $title-color;
		&:hover {
			color: $white-color;
		}
	}
	.slider-drag-cursor {
		color: $title-color;
	}
	.cursor-follower {
		mix-blend-mode: difference;
	}
}
.th-bg-dark {
	--th-body-background: #13182B;
}
.theme-yellow2 {
	--theme-color: #FFEC40;
	--theme-color2: #3282FB;
	.color-scheme-wrap .switchIcon {
		color: $black-color2;
	}
	.preloader .th-btn {
		color: $title-color;
		&:hover {
			color: $white-color;
		}
	}
	.slider-drag-cursor {
		color: $title-color;
	}
	.cursor-follower {
		mix-blend-mode: difference;
	}
}
iframe {
	border: none;
	width: 100%;
}

.slick-slide:focus,
button:focus,
a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
	outline: none;
}


input:focus {
	outline: none;
	box-shadow: none;
}

img:not([draggable]),
embed,
object,
video {
	max-width: 100%;
	height: auto;
}

ul {
	list-style-type: disc;
}

ol {
	list-style-type: decimal;
}

table {
	margin: 0 0 1.5em;
	width: 100%;
	border-collapse: collapse;
	border-spacing: 0;
	border: 1px solid $border-color;

}

th {
	font-weight: 700;
	color: $title-color;
}

td,
th {
	border: 1px solid $border-color;
	padding: 9px 12px;
}


a {
	color: $theme-color;
	text-decoration: none;
	outline: 0;
	transition: all ease 0.4s;

	&:hover {
		color: $title-color;
	}

	&:active,
	&:focus,
	&:hover,
	&:visited {
		text-decoration: none;
		outline: 0;
	}
}


button {
	transition: all ease 0.4s;
}

img {
	border: none;
	max-width: 100%;
}

ins {
	text-decoration: none;
}

pre {
	font-family: $body-font;
	background: #f5f5f5;
	color: #666;
	font-size: 14px;
	margin: 20px 0;
	overflow: auto;
	padding: 20px;
	white-space: pre-wrap;
	word-wrap: break-word;
}

span.ajax-loader:empty,
p:empty {
	display: none;
}


p {
	font-family: $body-font;
	margin: 0 0 18px 0;
	color: $body-color;
	line-height: $p-line-Height;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
p a,
span a {
	font-size: inherit;
	font-family: inherit;
	font-weight: inherit;
	line-height: inherit;
}


.h1,
h1,
.h2,
h2,
.h3,
h3,
.h4,
h4,
.h5,
h5,
.h6,
h6 {
	font-family: $title-font;
	color: $title-color;
	text-transform: none;
	font-weight: 700;
	line-height: 1.4;
	margin: 0 0 15px 0;
}

.h1,
h1 {
	font-size: 72px;
	line-height: 1.18;
}

.h2,
h2 {
	font-size: 56px;
	line-height: 1.227;
}

.h3,
h3 {
	font-size: 36px;
	line-height: 1.278;
}

.h4,
h4 {
	font-size: 30px;
	line-height: 1.333;
}

.h5,
h5 {
	font-size: 24px;
	line-height: 1.417;
}

.h6,
h6 {
	font-size: 20px;
	line-height: 1.5;
}
@include ml {
	.h1,
	h1 {
		font-size: 64px;
		line-height: 1.3;
	}
}
@include xl {
	.h1,
	h1 {
		font-size: 60px;
	}
}
@include lg {

	.h1,
	h1 {
		font-size: 55px;
		line-height: 1.3;
	}

	.h2,
	h2 {
		font-size: 36px;
		line-height: 1.3;
	}

	.h3,
	h3 {
		font-size: 30px;
	}

	.h4,
	h4 {
		font-size: 24px;
	}

	.h5,
	h5 {
		font-size: 20px;
	}

	.h6,
	h6 {
		font-size: 16px;
	}
}


@include sm {

	.h1,
	h1 {
		font-size: 40px;
	}

	.h2,
	h2 {
		font-size: 34px;
		line-height: 1.3;
	}

	.h3,
	h3 {
		font-size: 26px;
	}

	.h4,
	h4 {
		font-size: 22px;
	}

	.h5,
	h5 {
		font-size: 18px;
	}

	.h6,
	h6 {
		font-size: 16px;
	}
}

@include xs {

	.h1,
	h1 {
		font-size: 34px;
		line-height: 1.35;
	}
	.h2,
	h2 {
		font-size: 28px;
	}
}

@include vxs {

	.h1,
	h1 {
		font-size: 32px;
	}
}

.cursor-follower {
    position: fixed;
	background: $title-color;
    border: 1px solid $title-color;
    width: 15px;
    height: 15px;
    border-radius: 100%;
    z-index: 999999;
    -webkit-transition: 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background, 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) -webkit-transform;
    transition: 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background, 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) -webkit-transform;
    transition: 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) transform, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background;
    transition: 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) transform, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background, 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) -webkit-transform;
    user-select: none;
    pointer-events: none;
    transform: translate(2px, 2px);
	opacity: 1;
    mix-blend-mode: multiply;
	&.cursor-follower-big {
		transform: translate(2px, 2px) scale(3);
	}
}