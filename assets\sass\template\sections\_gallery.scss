/* Gallery Area 1 -------------------------------*/
.gallery-card {
    position: relative;
    overflow: hidden;
    display: inline-block;
    .gallery-img {
        position: relative;
        border-radius: 24px;
        overflow: hidden;
        img {
            width: 100%;
        }
        &:after {
            content: '';
            position: absolute;
            inset: 0px;
            background: $title-color;
            opacity: 0;
            border-radius: 0px;
            transition: 0.4s;
        }
    }
    .icon-btn {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -20%);
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        color: $white-color;
        z-index: 1;
        opacity: 0;
        backdrop-filter: blur(5px);
        --btn-size: 60px;
        --btn-font-size: 24px;
        &:hover {
            background: $theme-color;
            border-color: $theme-color;
            color: $white-color;
        }
    }
    @include md {
        .icon-btn {
            --btn-size: 50px;
            --btn-font-size: 24px;
        }
    }
}
.gallery-slider1 {
    .swiper-slide {
        &:hover {
            .gallery-img {
                &:after {
                    opacity: 0.7;
                }
            }
            .icon-btn {
                transform: translate(-50%, -50%);
                opacity: 1;
            }
        }
    }
    .slider-pagination {
        height: 34px;
        @include xs {
            display: none;
        }
    }
}

/* Gallery Area 2 -------------------------------*/
.gallery-card2 {
    position: relative;
    margin-bottom: 0;
    img {
        width: 100%;
    }
    .icon-btn {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0);
        width: auto;
        height: auto;
        background: transparent;
        font-size: 40px;
        z-index: 1;
    }
    &:after {
        content: '';
        position: absolute;
        inset: 0;
        background: $title-color;
        opacity: 0;
        transition: 0.4s;
    }
    &:hover {
        .icon-btn {
            transform: translate(-50%, -50%) scale(1);
        }
        &:after {
            opacity: 0.5;
        }
    }
}