@include ml {
    :root {
        --main-container: 1250px;
    }
}

@media (min-width: 1560px) {
    .th-container2 {
        width: 100%;
        max-width: 1560px;
        margin-left: auto;
        margin-right: 0;
        padding-left: 0;
        padding-right: 0;
        .container {
            --main-container: 1220px;
        }
    }
    .th-container3 {
        width: 100%;
        max-width: 1560px;
        margin-left: 0;
        margin-right: auto;
        padding-left: 0;
        padding-right: 0;
        .container {
            --main-container: 1220px;
        }
    }
}
@include hd {
    .th-container3,
    .th-container2 {
        margin-right: auto;
    }
}

@media only screen and (min-width: 1300px) {
    .container-xxl,
    .container-xl,
    .container-lg,
    .container-md,
    .container-sm,
    .container {
        max-width: calc(var(--main-container) + var(--container-gutters));
        padding-left: calc(var(--container-gutters) / 2);
        padding-right: calc(var(--container-gutters) / 2);
        &.px-0 {
            max-width: var(--main-container);
        }
    }
}

@media only screen and (min-width: 1300px) {
    .th-container {
        --main-container: 1396px;
    }
}

@media only screen and (max-width: 1600px) {
    .container-fluid.px-0 {
        padding-left: 15px !important;
        padding-right: 15px !important;

        .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
    }
}

.container-gallery {
    max-width: 1840px;
}