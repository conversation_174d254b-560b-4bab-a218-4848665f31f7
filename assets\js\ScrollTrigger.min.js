/*!
 * ScrollTrigger 3.11.4
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(){return g||"undefined"!=typeof window&&(g=window.gsap)&&g.registerPlugin&&g}function n(e,t){return~A.indexOf(e)&&A[A.indexOf(e)+1][t]}function r(e){return!!~k.indexOf(e)}function o(e,t,n,r,o){return e.addEventListener(t,n,{passive:!r,capture:!!o})}function i(e,t,n,r){return e.removeEventListener(t,n,!!r)}function a(){return E&&E.isPressed||O.cache++}function s(e,t){function n(r){if(r||0===r){M&&(m.history.scrollRestoration="manual");var o=E&&E.isPressed;r=n.v=Math.round(r)||(E&&E.iOS?1:0),e(r),n.cacheID=O.cache,o&&R("ss",r)}else(t||O.cache!==n.cacheID||R("ref"))&&(n.cacheID=O.cache,n.v=e());return n.v+n.offset}return n.offset=0,e&&n}function l(e){return g.utils.toArray(e)[0]||("string"==typeof e&&!1!==g.config().nullTargetWarn?console.warn("Element not found:",e):null)}function c(e,t){var o=t.s,i=t.sc;r(e)&&(e=y.scrollingElement||b);var l=O.indexOf(e),c=i===z.sc?1:2;~l||(l=O.push(e)-1),O[l+c]||e.addEventListener("scroll",a);var u=O[l+c],f=u||(O[l+c]=s(n(e,o),!0)||(r(e)?i:s((function(t){return arguments.length?e[o]=t:e[o]}))));return f.target=e,u||(f.smooth="smooth"===g.getProperty(e,"scrollBehavior")),f}function u(e,t,n){function r(e,t){var r=D();t||l<r-a?(i=o,o=e,s=a,a=r):n?o+=e:o=i+(e-i)/(r-s)*(a-s)}var o=e,i=e,a=D(),s=a,l=t||50,c=Math.max(500,3*l);return{update:r,reset:function(){i=o=n?0:o,s=a=0},getVelocity:function(e){var t=s,l=i,u=D();return!e&&0!==e||e===o||r(e),a===s||c<u-s?0:(o+(n?l:-l))/((n?u:a)-t)*1e3}}}function f(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function d(e){var t=Math.max.apply(Math,e),n=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(n)?t:n}function p(){(S=g.core.globals().ScrollTrigger)&&S.core&&function(){var e=S.core,t=e.bridge||{},n=e._scrollers,r=e._proxies;n.push.apply(n,O),r.push.apply(r,A),O=n,A=r,R=function(e,n){return t[e](n)}}()}function h(e){return(g=e||t())&&"undefined"!=typeof document&&document.body&&(m=window,b=(y=document).documentElement,x=y.body,k=[m,y,b,x],g.utils.clamp,P=g.core.context||function(){},_="onpointerenter"in x?"pointer":"mouse",w=B.isTouch=m.matchMedia&&m.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in m||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,T=B.eventTypes=("ontouchstart"in b?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in b?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout((function(){return M=0}),500),p(),v=1),v}var g,v,m,y,b,x,w,_,S,k,E,T,P,M=1,C=[],O=[],A=[],D=Date.now,R=function(e,t){return t},Y="scrollLeft",I="scrollTop",X={s:Y,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:s((function(e){return arguments.length?m.scrollTo(e,z.sc()):m.pageXOffset||y[Y]||b[Y]||x[Y]||0}))},z={s:I,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:X,sc:s((function(e){return arguments.length?m.scrollTo(X.sc(),e):m.pageYOffset||y[I]||b[I]||x[I]||0}))};X.op=z,O.cache=0;var B=(F.prototype.init=function(e){v||h(g)||console.warn("Please gsap.registerPlugin(Observer)"),S||p();var t=e.tolerance,n=e.dragMinimum,s=e.type,k=e.target,M=e.lineHeight,O=e.debounce,A=e.preventDefault,R=e.onStop,Y=e.onStopDelay,I=e.ignore,B=e.wheelSpeed,F=e.event,L=e.onDragStart,N=e.onDragEnd,W=e.onDrag,H=e.onPress,q=e.onRelease,V=e.onRight,U=e.onLeft,j=e.onUp,G=e.onDown,K=e.onChangeX,Z=e.onChangeY,$=e.onChange,J=e.onToggleX,Q=e.onToggleY,ee=e.onHover,te=e.onHoverEnd,ne=e.onMove,re=e.ignoreCheck,oe=e.isNormalizer,ie=e.onGestureStart,ae=e.onGestureEnd,se=e.onWheel,le=e.onEnable,ce=e.onDisable,ue=e.onClick,fe=e.scrollSpeed,de=e.capture,pe=e.allowClicks,he=e.lockAxis,ge=e.onLockAxis;function ve(){return Ze=D()}function me(e,t){return(Be.event=e)&&I&&~I.indexOf(e.target)||t&&Ve&&"touch"!==e.pointerType||re&&re(e,t)}function ye(){var e=Be.deltaX=d(Ge),n=Be.deltaY=d(Ke),r=Math.abs(e)>=t,o=Math.abs(n)>=t;$&&(r||o)&&$(Be,e,n,Ge,Ke),r&&(V&&0<Be.deltaX&&V(Be),U&&Be.deltaX<0&&U(Be),K&&K(Be),J&&Be.deltaX<0!=Fe<0&&J(Be),Fe=Be.deltaX,Ge[0]=Ge[1]=Ge[2]=0),o&&(G&&0<Be.deltaY&&G(Be),j&&Be.deltaY<0&&j(Be),Z&&Z(Be),Q&&Be.deltaY<0!=Le<0&&Q(Be),Le=Be.deltaY,Ke[0]=Ke[1]=Ke[2]=0),(Ye||Re)&&(ne&&ne(Be),Re&&(W(Be),Re=!1),Ye=!1),Xe&&!(Xe=!1)&&ge&&ge(Be),Ie&&(se(Be),Ie=!1),Ae=0}function be(e,t,n){Ge[n]+=e,Ke[n]+=t,Be._vx.update(e),Be._vy.update(t),O?Ae=Ae||requestAnimationFrame(ye):ye()}function xe(e,t){he&&!ze&&(Be.axis=ze=Math.abs(e)>Math.abs(t)?"x":"y",Xe=!0),"y"!==ze&&(Ge[2]+=e,Be._vx.update(e,!0)),"x"!==ze&&(Ke[2]+=t,Be._vy.update(t,!0)),O?Ae=Ae||requestAnimationFrame(ye):ye()}function we(e){if(!me(e,1)){var t=(e=f(e,A)).clientX,r=e.clientY,o=t-Be.x,i=r-Be.y,a=Be.isDragging;Be.x=t,Be.y=r,(a||Math.abs(Be.startX-t)>=n||Math.abs(Be.startY-r)>=n)&&(W&&(Re=!0),a||(Be.isDragging=!0),xe(o,i),a||L&&L(Be))}}function _e(e){if(!me(e,1)){i(oe?k:je,T[1],we,!0);var t=!isNaN(Be.y-Be.startY),n=Be.isDragging&&(3<Math.abs(Be.x-Be.startX)||3<Math.abs(Be.y-Be.startY)),r=f(e);!n&&t&&(Be._vx.reset(),Be._vy.reset(),A&&pe&&g.delayedCall(.08,(function(){if(300<D()-Ze&&!e.defaultPrevented)if(e.target.click)e.target.click();else if(je.createEvent){var t=je.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,m,1,r.screenX,r.screenY,r.clientX,r.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(t)}}))),Be.isDragging=Be.isGesturing=Be.isPressed=!1,R&&!oe&&De.restart(!0),N&&n&&N(Be),q&&q(Be,n)}}function Se(e){return e.touches&&1<e.touches.length&&(Be.isGesturing=!0)&&ie(e,Be.isDragging)}function ke(){return(Be.isGesturing=!1)||ae(Be)}function Ee(e){if(!me(e)){var t=Ne(),n=We();be((t-He)*fe,(n-qe)*fe,1),He=t,qe=n,R&&De.restart(!0)}}function Te(e){if(!me(e)){e=f(e,A),se&&(Ie=!0);var t=(1===e.deltaMode?M:2===e.deltaMode?m.innerHeight:1)*B;be(e.deltaX*t,e.deltaY*t,0),R&&!oe&&De.restart(!0)}}function Pe(e){if(!me(e)){var t=e.clientX,n=e.clientY,r=t-Be.x,o=n-Be.y;Be.x=t,Be.y=n,Ye=!0,(r||o)&&xe(r,o)}}function Me(e){Be.event=e,ee(Be)}function Ce(e){Be.event=e,te(Be)}function Oe(e){return me(e)||f(e,A)&&ue(Be)}this.target=k=l(k)||b,this.vars=e,I=I&&g.utils.toArray(I),t=t||1e-9,n=n||0,B=B||1,fe=fe||1,s=s||"wheel,touch,pointer",O=!1!==O,M=M||parseFloat(m.getComputedStyle(x).lineHeight)||22;var Ae,De,Re,Ye,Ie,Xe,ze,Be=this,Fe=0,Le=0,Ne=c(k,X),We=c(k,z),He=Ne(),qe=We(),Ve=~s.indexOf("touch")&&!~s.indexOf("pointer")&&"pointerdown"===T[0],Ue=r(k),je=k.ownerDocument||y,Ge=[0,0,0],Ke=[0,0,0],Ze=0,$e=Be.onPress=function(e){me(e,1)||(Be.axis=ze=null,De.pause(),Be.isPressed=!0,e=f(e),Fe=Le=0,Be.startX=Be.x=e.clientX,Be.startY=Be.y=e.clientY,Be._vx.reset(),Be._vy.reset(),o(oe?k:je,T[1],we,A,!0),Be.deltaX=Be.deltaY=0,H&&H(Be))};De=Be._dc=g.delayedCall(Y||.25,(function(){Be._vx.reset(),Be._vy.reset(),De.pause(),R&&R(Be)})).pause(),Be.deltaX=Be.deltaY=0,Be._vx=u(0,50,!0),Be._vy=u(0,50,!0),Be.scrollX=Ne,Be.scrollY=We,Be.isDragging=Be.isGesturing=Be.isPressed=!1,P(this),Be.enable=function(e){return Be.isEnabled||(o(Ue?je:k,"scroll",a),0<=s.indexOf("scroll")&&o(Ue?je:k,"scroll",Ee,A,de),0<=s.indexOf("wheel")&&o(k,"wheel",Te,A,de),(0<=s.indexOf("touch")&&w||0<=s.indexOf("pointer"))&&(o(k,T[0],$e,A,de),o(je,T[2],_e),o(je,T[3],_e),pe&&o(k,"click",ve,!1,!0),ue&&o(k,"click",Oe),ie&&o(je,"gesturestart",Se),ae&&o(je,"gestureend",ke),ee&&o(k,_+"enter",Me),te&&o(k,_+"leave",Ce),ne&&o(k,_+"move",Pe)),Be.isEnabled=!0,e&&e.type&&$e(e),le&&le(Be)),Be},Be.disable=function(){Be.isEnabled&&(C.filter((function(e){return e!==Be&&r(e.target)})).length||i(Ue?je:k,"scroll",a),Be.isPressed&&(Be._vx.reset(),Be._vy.reset(),i(oe?k:je,T[1],we,!0)),i(Ue?je:k,"scroll",Ee,de),i(k,"wheel",Te,de),i(k,T[0],$e,de),i(je,T[2],_e),i(je,T[3],_e),i(k,"click",ve,!0),i(k,"click",Oe),i(je,"gesturestart",Se),i(je,"gestureend",ke),i(k,_+"enter",Me),i(k,_+"leave",Ce),i(k,_+"move",Pe),Be.isEnabled=Be.isPressed=Be.isDragging=!1,ce&&ce(Be))},Be.kill=Be.revert=function(){Be.disable();var e=C.indexOf(Be);0<=e&&C.splice(e,1),E===Be&&(E=0)},C.push(Be),oe&&r(k)&&(E=Be),Be.enable(F)},function(e,t,n){t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t)}(F,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),F);function F(e){this.init(e)}function L(){return Fe=1}function N(){return Fe=0}function W(e){return e}function H(e){return Math.round(1e5*e)/1e5||0}function q(){return"undefined"!=typeof window}function V(){return Te||q()&&(Te=window.gsap)&&Te.registerPlugin&&Te}function U(e){return!!~De.indexOf(e)}function j(e){return n(e,"getBoundingClientRect")||(U(e)?function(){return qt.width=Me.innerWidth,qt.height=Me.innerHeight,qt}:function(){return kt(e)})}function G(e,t){var r=t.s,o=t.d2,i=t.d,a=t.a;return(r="scroll"+o)&&(a=n(e,r))?a()-j(e)()[i]:U(e)?(Oe[r]||Ae[r])-(Me["inner"+o]||Oe["client"+o]||Ae["client"+o]):e[r]-e["offset"+o]}function K(e,t){for(var n=0;n<qe.length;n+=3)t&&!~t.indexOf(qe[n+1])||e(qe[n],qe[n+1],qe[n+2])}function Z(e){return"string"==typeof e}function $(e){return"function"==typeof e}function J(e){return"number"==typeof e}function Q(e){return"object"==typeof e}function ee(e,t,n){return e&&e.progress(t?0:1)&&n&&e.pause()}function te(e,t){if(e.enabled){var n=t(e);n&&n.totalTime&&(e.callbackAnimation=n)}}function ne(e){return Me.getComputedStyle(e)}function re(e,t){for(var n in t)n in e||(e[n]=t[n]);return e}function oe(e,t){var n=t.d2;return e["offset"+n]||e["client"+n]||0}function ie(e){var t,n=[],r=e.labels,o=e.duration();for(t in r)n.push(r[t]/o);return n}function ae(e){var t=Te.utils.snap(e),n=Array.isArray(e)&&e.slice(0).sort((function(e,t){return e-t}));return n?function(e,r,o){var i;if(void 0===o&&(o=.001),!r)return t(e);if(0<r){for(e-=o,i=0;i<n.length;i++)if(n[i]>=e)return n[i];return n[i-1]}for(i=n.length,e+=o;i--;)if(n[i]<=e)return n[i];return n[0]}:function(n,r,o){void 0===o&&(o=.001);var i=t(n);return!r||Math.abs(i-n)<o||i-n<0==r<0?i:t(r<0?n-e:n+e)}}function se(e,t,n,r){return n.split(",").forEach((function(n){return e(t,n,r)}))}function le(e,t,n,r,o){return e.addEventListener(t,n,{passive:!r,capture:!!o})}function ce(e,t,n,r){return e.removeEventListener(t,n,!!r)}function ue(e,t,n){return n&&n.wheelHandler&&e(t,"wheel",n)}function fe(e,t){if(Z(e)){var n=e.indexOf("="),r=~n?(e.charAt(n-1)+1)*parseFloat(e.substr(n+1)):0;~n&&(e.indexOf("%")>n&&(r*=t/100),e=e.substr(0,n-1)),e=r+(e in Pt?Pt[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function de(e,t,r,o,i,a,s,l){var c=i.startColor,u=i.endColor,f=i.fontSize,d=i.indent,p=i.fontWeight,h=Ce.createElement("div"),g=U(r)||"fixed"===n(r,"pinType"),v=-1!==e.indexOf("scroller"),m=g?Ae:r,y=-1!==e.indexOf("start"),b=y?c:u,x="border-color:"+b+";font-size:"+f+";color:"+b+";font-weight:"+p+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return x+="position:"+((v||l)&&g?"fixed;":"absolute;"),!v&&!l&&g||(x+=(o===z?dt:pt)+":"+(a+parseFloat(d))+"px;"),s&&(x+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),h._isStart=y,h.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),h.style.cssText=x,h.innerText=t||0===t?e+"-"+t:e,m.children[0]?m.insertBefore(h,m.children[0]):m.appendChild(h),h._offset=h["offset"+o.op.d2],Mt(h,0,o,y),h}function pe(){return 34<st()-ct&&(nt=nt||requestAnimationFrame(Ft))}function he(){Ge&&Ge.isPressed&&!(Ge.startX>Ae.clientWidth)||(O.cache++,Ge?nt=nt||requestAnimationFrame(Ft):Ft(),ct||Rt("scrollStart"),ct=st())}function ge(){$e=Me.innerWidth,Ze=Me.innerHeight}function ve(){O.cache++,Be||je||Ce.fullscreenElement||Ce.webkitFullscreenElement||Ke&&$e===Me.innerWidth&&!(Math.abs(Me.innerHeight-Ze)>.25*Me.innerHeight)||Re.restart(!0)}function me(){return ce(Ut,"scrollEnd",me)||Xt(!0)}function ye(e){for(var t=0;t<Yt.length;t+=5)(!e||Yt[t+4]&&Yt[t+4].query===e)&&(Yt[t].style.cssText=Yt[t+1],Yt[t].getBBox&&Yt[t].setAttribute("transform",Yt[t+2]||""),Yt[t+3].uncache=1)}function be(e,t){var n;for(Ne=0;Ne<Ct.length;Ne++)!(n=Ct[Ne])||t&&n._ctx!==t||(e?n.kill(1):n.revert(!0,!0));t&&ye(t),t||Rt("revert")}function xe(e,t){O.cache++,!t&&rt||O.forEach((function(e){return $(e)&&e.cacheID++&&(e.rec=0)})),Z(e)&&(Me.history.scrollRestoration=et=e)}function we(e,t,n,r){if(!e._gsap.swappedIn){for(var o,i=Lt.length,a=t.style,s=e.style;i--;)a[o=Lt[i]]=n[o];a.position="absolute"===n.position?"absolute":"relative","inline"===n.display&&(a.display="inline-block"),s[pt]=s[dt]="auto",a.flexBasis=n.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[ht]=oe(e,X)+St,a[gt]=oe(e,z)+St,a[xt]=s[wt]=s.top=s.left="0",Ht(r),s[ht]=s.maxWidth=n[ht],s[gt]=s.maxHeight=n[gt],s[xt]=n[xt],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}}function _e(e){for(var t=Nt.length,n=e.style,r=[],o=0;o<t;o++)r.push(Nt[o],n[Nt[o]]);return r.t=e,r}function Se(e,t,n,r,o,i,a,s,c,u,f,d,p){$(e)&&(e=e(s)),Z(e)&&"max"===e.substr(0,3)&&(e=d+("="===e.charAt(4)?fe("0"+e.substr(3),n):0));var h,g,v,m=p?p.time():0;if(p&&p.seek(0),J(e))a&&Mt(a,n,r,!0);else{$(t)&&(t=t(s));var y,b,x,w,_=(e||"0").split(" ");v=l(t)||Ae,(y=kt(v)||{})&&(y.left||y.top)||"none"!==ne(v).display||(w=v.style.display,v.style.display="block",y=kt(v),w?v.style.display=w:v.style.removeProperty("display")),b=fe(_[0],y[r.d]),x=fe(_[1]||"0",n),e=y[r.p]-c[r.p]-u+b+o-x,a&&Mt(a,x,r,n-x<20||a._isStart&&20<x),n-=n-x}if(i){var S=e+n,k=i._isStart;h="scroll"+r.d2,Mt(i,S,r,k&&20<S||!k&&(f?Math.max(Ae[h],Oe[h]):i.parentNode[h])<=S+1),f&&(c=kt(a),f&&(i.style[r.op.p]=c[r.op.p]-r.op.m-i._offset+St))}return p&&v&&(h=kt(v),p.seek(d),g=kt(v),p._caScrollDist=h[r.p]-g[r.p],e=e/p._caScrollDist*d),p&&p.seek(m),p?e:Math.round(e)}function ke(e,t,n,r){if(e.parentNode!==t){var o,i,a=e.style;if(t===Ae){for(o in e._stOrig=a.cssText,i=ne(e))+o||Vt.test(o)||!i[o]||"string"!=typeof a[o]||"0"===o||(a[o]=i[o]);a.top=n,a.left=r}else a.cssText=e._stOrig;Te.core.getCache(e).uncache=1,t.appendChild(e)}}function Ee(e,t){function n(t,s,l,c,u){var f=n.tween,d=s.onComplete;return l=l||i(),u=c&&u||0,c=c||t-l,f&&f.kill(),r=Math.round(l),s[a]=t,(s.modifiers={})[a]=function(e){return(e=Math.round(i()))!==r&&e!==o&&3<Math.abs(e-r)&&3<Math.abs(e-o)?(f.kill(),n.tween=0):e=l+c*f.ratio+u*f.ratio*f.ratio,o=r,r=Math.round(e)},s.onUpdate=function(){O.cache++,Ft()},s.onComplete=function(){n.tween=0,d&&d.call(f)},f=n.tween=Te.to(e,s)}var r,o,i=c(e,t),a="_scroll"+t.p2;return(e[a]=i).wheelHandler=function(){return n.tween&&n.tween.kill()&&(n.tween=0)},le(e,"wheel",i.wheelHandler),n}B.version="3.11.4",B.create=function(e){return new B(e)},B.register=h,B.getAll=function(){return C.slice()},B.getById=function(e){return C.filter((function(t){return t.vars.id===e}))[0]},t()&&g.registerPlugin(B);var Te,Pe,Me,Ce,Oe,Ae,De,Re,Ye,Ie,Xe,ze,Be,Fe,Le,Ne,We,He,qe,Ve,Ue,je,Ge,Ke,Ze,$e,Je,Qe,et,tt,nt,rt,ot,it,at=1,st=Date.now,lt=st(),ct=0,ut=0,ft=Math.abs,dt="right",pt="bottom",ht="width",gt="height",vt="Right",mt="Left",yt="Top",bt="Bottom",xt="padding",wt="margin",_t="Width",St="px",kt=function(e,t){var n=t&&"matrix(1, 0, 0, 1, 0, 0)"!==ne(e)[Le]&&Te.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),r=e.getBoundingClientRect();return n&&n.progress(0).kill(),r},Et={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Tt={toggleActions:"play",anticipatePin:0},Pt={top:0,left:0,center:.5,bottom:1,right:1},Mt=function(e,t,n,r){var o={display:"block"},i=n[r?"os2":"p2"],a=n[r?"p2":"os2"];e._isFlipped=r,o[n.a+"Percent"]=r?-100:0,o[n.a]=r?"1px":0,o["border"+i+_t]=1,o["border"+a+_t]=0,o[n.p]=t+"px",Te.set(e,o)},Ct=[],Ot={},At={},Dt=[],Rt=function(e){return At[e]&&At[e].map((function(e){return e()}))||Dt},Yt=[],It=0,Xt=function(e,t){if(!ct||e){rt=Ut.isRefreshing=!0,O.forEach((function(e){return $(e)&&e.cacheID++&&(e.rec=e())}));var n=Rt("refreshInit");Ve&&Ut.sort(),t||be(),O.forEach((function(e){$(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))})),Ct.slice(0).forEach((function(e){return e.refresh()})),Ct.forEach((function(e,t){if(e._subPinOffset&&e.pin){var n=e.vars.horizontal?"offsetWidth":"offsetHeight",r=e.pin[n];e.revert(!0,1),e.adjustPinSpacing(e.pin[n]-r),e.revert(!1,1)}})),Ct.forEach((function(e){return"max"===e.vars.end&&e.setPositions(e.start,Math.max(e.start+1,G(e.scroller,e._dir)))})),n.forEach((function(e){return e&&e.render&&e.render(-1)})),O.forEach((function(e){$(e)&&(e.smooth&&requestAnimationFrame((function(){return e.target.style.scrollBehavior="smooth"})),e.rec&&e(e.rec))})),xe(et,1),Re.pause(),It++,Ft(2),Ct.forEach((function(e){return $(e.vars.onRefresh)&&e.vars.onRefresh(e)})),rt=Ut.isRefreshing=!1,Rt("refresh")}else le(Ut,"scrollEnd",me)},zt=0,Bt=1,Ft=function(e){if(!rt||2===e){Ut.isUpdating=!0,it&&it.update(0);var t=Ct.length,n=st(),r=50<=n-lt,o=t&&Ct[0].scroll();if(Bt=o<zt?-1:1,zt=o,r&&(ct&&!Fe&&200<n-ct&&(ct=0,Rt("scrollEnd")),Xe=lt,lt=n),Bt<0){for(Ne=t;0<Ne--;)Ct[Ne]&&Ct[Ne].update(0,r);Bt=1}else for(Ne=0;Ne<t;Ne++)Ct[Ne]&&Ct[Ne].update(0,r);Ut.isUpdating=!1}nt=0},Lt=["left","top",pt,dt,wt+bt,wt+vt,wt+yt,wt+mt,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Nt=Lt.concat([ht,gt,"boxSizing","max"+_t,"maxHeight","position",wt,xt,xt+yt,xt+vt,xt+bt,xt+mt]),Wt=/([A-Z])/g,Ht=function(e){if(e){var t,n,r=e.t.style,o=e.length,i=0;for((e.t._gsap||Te.core.getCache(e.t)).uncache=1;i<o;i+=2)n=e[i+1],t=e[i],n?r[t]=n:r[t]&&r.removeProperty(t.replace(Wt,"-$1").toLowerCase())}},qt={left:0,top:0},Vt=/(webkit|moz|length|cssText|inset)/i,Ut=(jt.prototype.init=function(e,t){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),ut){var r,o,i,a,s,u,f,d,p,h,g,v,m,y,b,x,w,_,S,k,E,T,P,M,C,D,R,Y,I,B,F,L,N,q,V,K,se,ue,pe,ge=(e=re(Z(e)||J(e)||e.nodeType?{trigger:e}:e,Tt)).onUpdate,ye=e.toggleClass,be=e.id,xe=e.onToggle,Pe=e.onRefresh,De=e.scrub,Re=e.trigger,ze=e.pin,Le=e.pinSpacing,We=e.invalidateOnRefresh,He=e.anticipatePin,qe=e.onScrubComplete,je=e.onSnapComplete,Ge=e.once,Ke=e.snap,Ze=e.pinReparent,$e=e.pinSpacer,Je=e.containerAnimation,et=e.fastScrollEnd,nt=e.preventOverlaps,lt=e.horizontal||e.containerAnimation&&!1!==e.horizontal?X:z,dt=!De&&0!==De,pt=l(e.scroller||Me),Pt=Te.core.getCache(pt),Mt=U(pt),At="fixed"===("pinType"in e?e.pinType:n(pt,"pinType")||Mt&&"fixed"),Dt=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],Rt=dt&&e.toggleActions.split(" "),Yt="markers"in e?e.markers:Tt.markers,zt=Mt?0:parseFloat(ne(pt)["border"+lt.p2+_t])||0,Ft=this,Lt=e.onRefreshInit&&function(){return e.onRefreshInit(Ft)},Nt=function(e,t,r){var o=r.d,i=r.d2,a=r.a;return(a=n(e,"getBoundingClientRect"))?function(){return a()[o]}:function(){return(t?Me["inner"+i]:e["client"+i])||0}}(pt,Mt,lt),Wt=function(e,t){return!t||~A.indexOf(e)?j(e):function(){return qt}}(pt,Mt),Vt=0,Ut=0,Gt=c(pt,lt);if(Qe(Ft),Ft._dir=lt,He*=45,Ft.scroller=pt,Ft.scroll=Je?Je.time.bind(Je):Gt,a=Gt(),Ft.vars=e,t=t||e.animation,"refreshPriority"in e&&(Ve=1,-9999===e.refreshPriority&&(it=Ft)),Pt.tweenScroll=Pt.tweenScroll||{top:Ee(pt,z),left:Ee(pt,X)},Ft.tweenTo=r=Pt.tweenScroll[lt.p],Ft.scrubDuration=function(e){(L=J(e)&&e)?F?F.duration(e):F=Te.to(t,{ease:"expo",totalProgress:"+=0.001",duration:L,paused:!0,onComplete:function(){return qe&&qe(Ft)}}):(F&&F.progress(1).kill(),F=0)},t&&(t.vars.lazy=!1,t._initted||!1!==t.vars.immediateRender&&!1!==e.immediateRender&&t.duration()&&t.render(0,!0,!0),Ft.animation=t.pause(),(t.scrollTrigger=Ft).scrubDuration(De),I=0,be=be||t.vars.id),Ct.push(Ft),Ke&&(Q(Ke)&&!Ke.push||(Ke={snapTo:Ke}),"scrollBehavior"in Ae.style&&Te.set(Mt?[Ae,Oe]:pt,{scrollBehavior:"auto"}),O.forEach((function(e){return $(e)&&e.target===(Mt?Ce.scrollingElement||Oe:pt)&&(e.smooth=!1)})),i=$(Ke.snapTo)?Ke.snapTo:"labels"===Ke.snapTo?function(e){return function(t){return Te.utils.snap(ie(e),t)}}(t):"labelsDirectional"===Ke.snapTo?function(e){return function(t,n){return ae(ie(e))(t,n.direction)}}(t):!1!==Ke.directional?function(e,t){return ae(Ke.snapTo)(e,st()-Ut<500?0:t.direction)}:Te.utils.snap(Ke.snapTo),N=Q(N=Ke.duration||{min:.1,max:2})?Ie(N.min,N.max):Ie(N,N),q=Te.delayedCall(Ke.delay||L/2||.1,(function(){var e=Gt(),n=st()-Ut<500,o=r.tween;if(!(n||Math.abs(Ft.getVelocity())<10)||o||Fe||Vt===e)Ft.isActive&&Vt!==e&&q.restart(!0);else{var a=(e-u)/m,s=t&&!dt?t.totalProgress():a,l=n?0:(s-B)/(st()-Xe)*1e3||0,c=Te.utils.clamp(-a,1-a,ft(l/2)*l/.185),d=a+(!1===Ke.inertia?0:c),p=Ie(0,1,i(d,Ft)),h=Math.round(u+p*m),g=Ke.onStart,v=Ke.onInterrupt,y=Ke.onComplete;if(e<=f&&u<=e&&h!==e){if(o&&!o._initted&&o.data<=ft(h-e))return;!1===Ke.inertia&&(c=p-a),r(h,{duration:N(ft(.185*Math.max(ft(d-s),ft(p-s))/l/.05||0)),ease:Ke.ease||"power3",data:ft(h-e),onInterrupt:function(){return q.restart(!0)&&v&&v(Ft)},onComplete:function(){Ft.update(),Vt=Gt(),I=B=t&&!dt?t.totalProgress():Ft.progress,je&&je(Ft),y&&y(Ft)}},e,c*m,h-e-c*m),g&&g(Ft,r.tween)}}})).pause()),be&&(Ot[be]=Ft),pe=(pe=(Re=Ft.trigger=l(Re||ze))&&Re._gsap&&Re._gsap.stRevert)&&pe(Ft),ze=!0===ze?Re:l(ze),Z(ye)&&(ye={targets:Re,className:ye}),ze&&(!1===Le||Le===wt||(Le=!(!Le&&ze.parentNode&&ze.parentNode.style&&"flex"===ne(ze.parentNode).display)&&xt),Ft.pin=ze,(o=Te.core.getCache(ze)).spacer?y=o.pinState:($e&&(($e=l($e))&&!$e.nodeType&&($e=$e.current||$e.nativeElement),o.spacerIsNative=!!$e,$e&&(o.spacerState=_e($e))),o.spacer=w=$e||Ce.createElement("div"),w.classList.add("pin-spacer"),be&&w.classList.add("pin-spacer-"+be),o.pinState=y=_e(ze)),!1!==e.force3D&&Te.set(ze,{force3D:!0}),Ft.spacer=w=o.spacer,Y=ne(ze),P=Y[Le+lt.os2],S=Te.getProperty(ze),k=Te.quickSetter(ze,lt.a,St),we(ze,w,Y),x=_e(ze)),Yt){v=Q(Yt)?re(Yt,Et):Et,h=de("scroller-start",be,pt,lt,v,0),g=de("scroller-end",be,pt,lt,v,0,h),_=h["offset"+lt.op.d2];var Kt=l(n(pt,"content")||pt);d=this.markerStart=de("start",be,Kt,lt,v,_,0,Je),p=this.markerEnd=de("end",be,Kt,lt,v,_,0,Je),Je&&(ue=Te.quickSetter([d,p],lt.a,St)),At||A.length&&!0===n(pt,"fixedMarkers")||(function(e){var t=ne(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"}(Mt?Ae:pt),Te.set([h,g],{force3D:!0}),C=Te.quickSetter(h,lt.a,St),R=Te.quickSetter(g,lt.a,St))}if(Je){var Zt=Je.vars.onUpdate,$t=Je.vars.onUpdateParams;Je.eventCallback("onUpdate",(function(){Ft.update(0,0,1),Zt&&Zt.apply($t||[])}))}Ft.previous=function(){return Ct[Ct.indexOf(Ft)-1]},Ft.next=function(){return Ct[Ct.indexOf(Ft)+1]},Ft.revert=function(e,n){if(!n)return Ft.kill(!0);var r=!1!==e||!Ft.enabled,o=Be;r!==Ft.isReverted&&(r&&(K=Math.max(Gt(),Ft.scroll.rec||0),V=Ft.progress,se=t&&t.progress()),d&&[d,p,h,g].forEach((function(e){return e.style.display=r?"none":"block"})),r&&(Be=1,Ft.update(r)),!ze||Ze&&Ft.isActive||(r?function(e,t,n){Ht(n);var r=e._gsap;if(r.spacerIsNative)Ht(r.spacerState);else if(e._gsap.swappedIn){var o=t.parentNode;o&&(o.insertBefore(e,t),o.removeChild(t))}e._gsap.swappedIn=!1}(ze,w,y):we(ze,w,ne(ze),M)),r||Ft.update(r),Be=o,Ft.isReverted=r)},Ft.refresh=function(n,o){if(!Be&&Ft.enabled||o)if(ze&&n&&ct)le(jt,"scrollEnd",me);else{!rt&&Lt&&Lt(Ft),Be=1,Ut=st(),r.tween&&(r.tween.kill(),r.tween=0),F&&F.pause(),We&&t&&t.revert({kill:!1}).invalidate(),Ft.isReverted||Ft.revert(!0,!0),Ft._subPinOffset=!1;for(var i,v,_,k,P,C,O,A,R,Y,I,B=Nt(),L=Wt(),N=Je?Je.duration():G(pt,lt),W=0,H=0,U=e.end,j=e.endTrigger||Re,Q=e.start||(0!==e.start&&Re?ze?"0 0":"0 100%":0),ee=Ft.pinnedContainer=e.pinnedContainer&&l(e.pinnedContainer),te=Re&&Math.max(0,Ct.indexOf(Ft))||0,re=te;re--;)(C=Ct[re]).end||C.refresh(0,1)||(Be=1),!(O=C.pin)||O!==Re&&O!==ze||C.isReverted||((Y=Y||[]).unshift(C),C.revert(!0,!0)),C!==Ct[re]&&(te--,re--);for($(Q)&&(Q=Q(Ft)),u=Se(Q,Re,B,lt,Gt(),d,h,Ft,L,zt,At,N,Je)||(ze?-.001:0),$(U)&&(U=U(Ft)),Z(U)&&!U.indexOf("+=")&&(~U.indexOf(" ")?U=(Z(Q)?Q.split(" ")[0]:"")+U:(W=fe(U.substr(2),B),U=Z(Q)?Q:u+W,j=Re)),f=Math.max(u,Se(U||(j?"100% 0":N),j,B,lt,Gt()+W,p,g,Ft,L,zt,At,N,Je))||-.001,m=f-u||(u-=.01)&&.001,W=0,re=te;re--;)(O=(C=Ct[re]).pin)&&C.start-C._pinPush<=u&&!Je&&0<C.end&&(i=C.end-C.start,(O===Re&&C.start-C._pinPush<u||O===ee)&&!J(Q)&&(W+=i*(1-C.progress)),O===ze&&(H+=i));if(u+=W,f+=W,Ft._pinPush=H,d&&W&&((i={})[lt.a]="+="+W,ee&&(i[lt.p]="-="+Gt()),Te.set([d,p],i)),ze)i=ne(ze),k=lt===z,_=Gt(),E=parseFloat(S(lt.a))+H,!N&&1<f&&((I={style:I=(Mt?Ce.scrollingElement||Oe:pt).style,value:I["overflow"+lt.a.toUpperCase()]})["overflow"+lt.a.toUpperCase()]="scroll"),we(ze,w,i),x=_e(ze),v=kt(ze,!0),A=At&&c(pt,k?X:z)(),Le&&((M=[Le+lt.os2,m+H+St]).t=w,(re=Le===xt?oe(ze,lt)+m+H:0)&&M.push(lt.d,re+St),Ht(M),ee&&Ct.forEach((function(e){e.pin===ee&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)})),At&&Gt(K)),At&&((P={top:v.top+(k?_-u:A)+St,left:v.left+(k?A:_-u)+St,boxSizing:"border-box",position:"fixed"})[ht]=P.maxWidth=Math.ceil(v.width)+St,P[gt]=P.maxHeight=Math.ceil(v.height)+St,P[wt]=P[wt+yt]=P[wt+vt]=P[wt+bt]=P[wt+mt]="0",P[xt]=i[xt],P[xt+yt]=i[xt+yt],P[xt+vt]=i[xt+vt],P[xt+bt]=i[xt+bt],P[xt+mt]=i[xt+mt],b=function(e,t,n){for(var r,o=[],i=e.length,a=n?8:0;a<i;a+=2)r=e[a],o.push(r,r in t?t[r]:e[a+1]);return o.t=e.t,o}(y,P,Ze),rt&&Gt(0)),t?(R=t._initted,Ue(1),t.render(t.duration(),!0,!0),T=S(lt.a)-E+m+H,D=1<Math.abs(m-T),At&&D&&b.splice(b.length-2,2),t.render(0,!0,!0),R||t.invalidate(!0),t.parent||t.totalTime(t.totalTime()),Ue(0)):T=m,I&&(I.value?I.style["overflow"+lt.a.toUpperCase()]=I.value:I.style.removeProperty("overflow-"+lt.a));else if(Re&&Gt()&&!Je)for(v=Re.parentNode;v&&v!==Ae;)v._pinOffset&&(u-=v._pinOffset,f-=v._pinOffset),v=v.parentNode;Y&&Y.forEach((function(e){return e.revert(!1,!0)})),Ft.start=u,Ft.end=f,a=s=rt?K:Gt(),Je||rt||(a<K&&Gt(K),Ft.scroll.rec=0),Ft.revert(!1,!0),q&&(Vt=-1,Ft.isActive&&Gt(u+m*V),q.restart(!0)),Be=0,t&&dt&&(t._initted||se)&&t.progress()!==se&&t.progress(se,!0).render(t.time(),!0,!0),V===Ft.progress&&!Je||(t&&!dt&&t.totalProgress(V,!0),Ft.progress=(a-u)/m===V?0:V),ze&&Le&&(w._pinOffset=Math.round(Ft.progress*T)),Pe&&!rt&&Pe(Ft)}},Ft.getVelocity=function(){return(Gt()-s)/(st()-Xe)*1e3||0},Ft.endAnimation=function(){ee(Ft.callbackAnimation),t&&(F?F.progress(1):t.paused()?dt||ee(t,Ft.direction<0,1):ee(t,t.reversed()))},Ft.labelToScroll=function(e){return t&&t.labels&&(u||Ft.refresh()||u)+t.labels[e]/t.duration()*m||0},Ft.getTrailing=function(e){var t=Ct.indexOf(Ft),n=0<Ft.direction?Ct.slice(0,t).reverse():Ct.slice(t+1);return(Z(e)?n.filter((function(t){return t.vars.preventOverlaps===e})):n).filter((function(e){return 0<Ft.direction?e.end<=u:e.start>=f}))},Ft.update=function(e,n,o){if(!Je||o||e){var i,l,c,d,p,g,v,y=rt?K:Ft.scroll(),_=e?0:(y-u)/m,S=_<0?0:1<_?1:_||0,M=Ft.progress;if(n&&(s=a,a=Je?Gt():y,Ke&&(B=I,I=t&&!dt?t.totalProgress():S)),He&&!S&&ze&&!Be&&!at&&ct&&u<y+(y-s)/(st()-Xe)*He&&(S=1e-4),S!==M&&Ft.enabled){if(d=(p=(i=Ft.isActive=!!S&&S<1)!=(!!M&&M<1))||!!S!=!!M,Ft.direction=M<S?1:-1,Ft.progress=S,d&&!Be&&(l=S&&!M?0:1===S?1:1===M?2:3,dt&&(c=!p&&"none"!==Rt[l+1]&&Rt[l+1]||Rt[l],v=t&&("complete"===c||"reset"===c||c in t))),nt&&(p||v)&&(v||De||!t)&&($(nt)?nt(Ft):Ft.getTrailing(nt).forEach((function(e){return e.endAnimation()}))),dt||(!F||Be||at?t&&t.totalProgress(S,!!Be):(F._dp._time-F._start!==F._time&&F.render(F._dp._time-F._start),F.resetTo?F.resetTo("totalProgress",S,t._tTime/t._tDur):(F.vars.totalProgress=S,F.invalidate().restart()))),ze)if(e&&Le&&(w.style[Le+lt.os2]=P),At){if(d){if(g=!e&&M<S&&y<f+1&&y+1>=G(pt,lt),Ze)if(e||!i&&!g)ke(ze,w);else{var O=kt(ze,!0),A=y-u;ke(ze,Ae,O.top+(lt===z?A:0)+St,O.left+(lt===z?0:A)+St)}Ht(i||g?b:x),D&&S<1&&i||k(E+(1!==S||g?0:T))}}else k(H(E+T*S));!Ke||r.tween||Be||at||q.restart(!0),ye&&(p||Ge&&S&&(S<1||!tt))&&Ye(ye.targets).forEach((function(e){return e.classList[i||Ge?"add":"remove"](ye.className)})),!ge||dt||e||ge(Ft),d&&!Be?(dt&&(v&&("complete"===c?t.pause().totalProgress(1):"reset"===c?t.restart(!0).pause():"restart"===c?t.restart(!0):t[c]()),ge&&ge(Ft)),!p&&tt||(xe&&p&&te(Ft,xe),Dt[l]&&te(Ft,Dt[l]),Ge&&(1===S?Ft.kill(!1,1):Dt[l]=0),p||Dt[l=1===S?1:3]&&te(Ft,Dt[l])),et&&!i&&Math.abs(Ft.getVelocity())>(J(et)?et:2500)&&(ee(Ft.callbackAnimation),F?F.progress(1):ee(t,"reverse"===c?1:!S,1))):dt&&ge&&!Be&&ge(Ft)}if(R){var Y=Je?y/Je.duration()*(Je._caScrollDist||0):y;C(Y+(h._isFlipped?1:0)),R(Y)}ue&&ue(-y/Je.duration()*(Je._caScrollDist||0))}},Ft.enable=function(e,t){Ft.enabled||(Ft.enabled=!0,le(pt,"resize",ve),le(Mt?Ce:pt,"scroll",he),Lt&&le(jt,"refreshInit",Lt),!1!==e&&(Ft.progress=V=0,a=s=Vt=Gt()),!1!==t&&Ft.refresh())},Ft.getTween=function(e){return e&&r?r.tween:F},Ft.setPositions=function(e,t){ze&&(E+=e-u,T+=t-e-m,Le===xt&&Ft.adjustPinSpacing(t-e-m)),Ft.start=u=e,Ft.end=f=t,m=t-e,Ft.update()},Ft.adjustPinSpacing=function(e){if(M){var t=M.indexOf(lt.d)+1;M[t]=parseFloat(M[t])+e+St,M[1]=parseFloat(M[1])+e+St,Ht(M)}},Ft.disable=function(e,t){if(Ft.enabled&&(!1!==e&&Ft.revert(!0,!0),Ft.enabled=Ft.isActive=!1,t||F&&F.pause(),K=0,o&&(o.uncache=1),Lt&&ce(jt,"refreshInit",Lt),q&&(q.pause(),r.tween&&r.tween.kill()&&(r.tween=0)),!Mt)){for(var n=Ct.length;n--;)if(Ct[n].scroller===pt&&Ct[n]!==Ft)return;ce(pt,"resize",ve),ce(pt,"scroll",he)}},Ft.kill=function(n,r){Ft.disable(n,r),F&&!r&&F.kill(),be&&delete Ot[be];var i=Ct.indexOf(Ft);0<=i&&Ct.splice(i,1),i===Ne&&0<Bt&&Ne--,i=0,Ct.forEach((function(e){return e.scroller===Ft.scroller&&(i=1)})),i||rt||(Ft.scroll.rec=0),t&&(t.scrollTrigger=null,n&&t.revert({kill:!1}),r||t.kill()),d&&[d,p,h,g].forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),it===Ft&&(it=0),ze&&(o&&(o.uncache=1),i=0,Ct.forEach((function(e){return e.pin===ze&&i++})),i||(o.spacer=0)),e.onKill&&e.onKill(Ft)},Ft.enable(!1,!1),pe&&pe(Ft),t&&t.add&&!m?Te.delayedCall(.01,(function(){return u||f||Ft.refresh()}))&&(m=.01)&&(u=f=0):Ft.refresh(),ze&&function(){if(ot!==It){var e=ot=It;requestAnimationFrame((function(){return e===It&&Xt(!0)}))}}()}else this.update=this.refresh=this.kill=W},jt.register=function(e){return Pe||(Te=e||V(),q()&&window.document&&jt.enable(),Pe=ut),Pe},jt.defaults=function(e){if(e)for(var t in e)Tt[t]=e[t];return Tt},jt.disable=function(e,t){ut=0,Ct.forEach((function(n){return n[t?"kill":"disable"](e)})),ce(Me,"wheel",he),ce(Ce,"scroll",he),clearInterval(ze),ce(Ce,"touchcancel",W),ce(Ae,"touchstart",W),se(ce,Ce,"pointerdown,touchstart,mousedown",L),se(ce,Ce,"pointerup,touchend,mouseup",N),Re.kill(),K(ce);for(var n=0;n<O.length;n+=3)ue(ce,O[n],O[n+1]),ue(ce,O[n],O[n+2])},jt.enable=function(){if(Me=window,Ce=document,Oe=Ce.documentElement,Ae=Ce.body,Te&&(Ye=Te.utils.toArray,Ie=Te.utils.clamp,Qe=Te.core.context||W,Ue=Te.core.suppressOverwrites||W,et=Me.history.scrollRestoration||"auto",Te.core.globals("ScrollTrigger",jt),Ae)){ut=1,B.register(Te),jt.isTouch=B.isTouch,Je=B.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),le(Me,"wheel",he),De=[Me,Ce,Oe,Ae],Te.matchMedia?(jt.matchMedia=function(e){var t,n=Te.matchMedia();for(t in e)n.add(t,e[t]);return n},Te.addEventListener("matchMediaInit",(function(){return be()})),Te.addEventListener("matchMediaRevert",(function(){return ye()})),Te.addEventListener("matchMedia",(function(){Xt(0,1),Rt("matchMedia")})),Te.matchMedia("(orientation: portrait)",(function(){return ge(),ge}))):console.warn("Requires GSAP 3.11.0 or later"),ge(),le(Ce,"scroll",he);var e,t,n=Ae.style,r=n.borderTopStyle,o=Te.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function(){return this.time(-.01,!0)}}),n.borderTopStyle="solid",e=kt(Ae),z.m=Math.round(e.top+z.sc())||0,X.m=Math.round(e.left+X.sc())||0,r?n.borderTopStyle=r:n.removeProperty("border-top-style"),ze=setInterval(pe,250),Te.delayedCall(.5,(function(){return at=0})),le(Ce,"touchcancel",W),le(Ae,"touchstart",W),se(le,Ce,"pointerdown,touchstart,mousedown",L),se(le,Ce,"pointerup,touchend,mouseup",N),Le=Te.utils.checkPrefix("transform"),Nt.push(Le),Pe=st(),Re=Te.delayedCall(.2,Xt).pause(),qe=[Ce,"visibilitychange",function(){var e=Me.innerWidth,t=Me.innerHeight;Ce.hidden?(We=e,He=t):We===e&&He===t||ve()},Ce,"DOMContentLoaded",Xt,Me,"load",Xt,Me,"resize",ve],K(le),Ct.forEach((function(e){return e.enable(0,1)})),t=0;t<O.length;t+=3)ue(ce,O[t],O[t+1]),ue(ce,O[t],O[t+2])}},jt.config=function(e){"limitCallbacks"in e&&(tt=!!e.limitCallbacks);var t=e.syncInterval;t&&clearInterval(ze)||(ze=t)&&setInterval(pe,t),"ignoreMobileResize"in e&&(Ke=1===jt.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(K(ce)||K(le,e.autoRefreshEvents||"none"),je=-1===(e.autoRefreshEvents+"").indexOf("resize"))},jt.scrollerProxy=function(e,t){var n=l(e),r=O.indexOf(n),o=U(n);~r&&O.splice(r,o?6:2),t&&(o?A.unshift(Me,t,Ae,t,Oe,t):A.unshift(n,t))},jt.clearMatchMedia=function(e){Ct.forEach((function(t){return t._ctx&&t._ctx.query===e&&t._ctx.kill(!0,!0)}))},jt.isInViewport=function(e,t,n){var r=(Z(e)?l(e):e).getBoundingClientRect(),o=r[n?ht:gt]*t||0;return n?0<r.right-o&&r.left+o<Me.innerWidth:0<r.bottom-o&&r.top+o<Me.innerHeight},jt.positionInViewport=function(e,t,n){Z(e)&&(e=l(e));var r=e.getBoundingClientRect(),o=r[n?ht:gt],i=null==t?o/2:t in Pt?Pt[t]*o:~t.indexOf("%")?parseFloat(t)*o/100:parseFloat(t)||0;return n?(r.left+i)/Me.innerWidth:(r.top+i)/Me.innerHeight},jt.killAll=function(e){if(Ct.slice(0).forEach((function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()})),!0!==e){var t=At.killAll||[];At={},t.forEach((function(e){return e()}))}},jt);function jt(e,t){Pe||jt.register(Te)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),this.init(e,t)}function Gt(e,t,n,r){return r<t?e(r):t<0&&e(0),r<n?(r-t)/(n-t):n<0?t/(t-n):1}function Kt(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(B.isTouch?" pinch-zoom":""):"none",e===Oe&&Kt(Ae,t)}function Zt(e){var t,n=e.event,r=e.target,o=e.axis,i=(n.changedTouches?n.changedTouches[0]:n).target,a=i._gsap||Te.core.getCache(i),s=st();if(!a._isScrollT||2e3<s-a._isScrollT){for(;i&&i!==Ae&&(i.scrollHeight<=i.clientHeight&&i.scrollWidth<=i.clientWidth||!Qt[(t=ne(i)).overflowY]&&!Qt[t.overflowX]);)i=i.parentNode;a._isScroll=i&&i!==r&&!U(i)&&(Qt[(t=ne(i)).overflowY]||Qt[t.overflowX]),a._isScrollT=s}!a._isScroll&&"x"!==o||(n.stopPropagation(),n._gsapAllow=!0)}function $t(e,t,n,r){return B.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:r=r&&Zt,onPress:r,onDrag:r,onScroll:r,onEnable:function(){return n&&le(Ce,B.eventTypes[0],tn,!1,!0)},onDisable:function(){return ce(Ce,B.eventTypes[0],tn,!0)}})}Ut.version="3.11.4",Ut.saveStyles=function(e){return e?Ye(e).forEach((function(e){if(e&&e.style){var t=Yt.indexOf(e);0<=t&&Yt.splice(t,5),Yt.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),Te.core.getCache(e),Qe())}})):Yt},Ut.revert=function(e,t){return be(!e,t)},Ut.create=function(e,t){return new Ut(e,t)},Ut.refresh=function(e){return e?ve():(Pe||Ut.register())&&Xt(!0)},Ut.update=function(e){return++O.cache&&Ft(!0===e?2:0)},Ut.clearScrollMemory=xe,Ut.maxScroll=function(e,t){return G(e,t?X:z)},Ut.getScrollFunc=function(e,t){return c(l(e),t?X:z)},Ut.getById=function(e){return Ot[e]},Ut.getAll=function(){return Ct.filter((function(e){return"ScrollSmoother"!==e.vars.id}))},Ut.isScrolling=function(){return!!ct},Ut.snapDirectional=ae,Ut.addEventListener=function(e,t){var n=At[e]||(At[e]=[]);~n.indexOf(t)||n.push(t)},Ut.removeEventListener=function(e,t){var n=At[e],r=n&&n.indexOf(t);0<=r&&n.splice(r,1)},Ut.batch=function(e,t){function n(e,t){var n=[],r=[],o=Te.delayedCall(a,(function(){t(n,r),n=[],r=[]})).pause();return function(e){n.length||o.restart(!0),n.push(e.trigger),r.push(e),s<=n.length&&o.progress(1)}}var r,o=[],i={},a=t.interval||.016,s=t.batchMax||1e9;for(r in t)i[r]="on"===r.substr(0,2)&&$(t[r])&&"onRefreshInit"!==r?n(0,t[r]):t[r];return $(s)&&(s=s(),le(Ut,"refresh",(function(){return s=t.batchMax()}))),Ye(e).forEach((function(e){var t={};for(r in i)t[r]=i[r];t.trigger=e,o.push(Ut.create(t))})),o};var Jt,Qt={auto:1,scroll:1},en=/(input|label|select|textarea)/i,tn=function(e){var t=en.test(e.target.tagName);(t||Jt)&&(e._gsapAllow=!0,Jt=t)};Ut.sort=function(e){return Ct.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+e.start-(t.start+-1e6*(t.vars.refreshPriority||0))})},Ut.observe=function(e){return new B(e)},Ut.normalizeScroll=function(e){if(void 0===e)return Ge;if(!0===e&&Ge)return Ge.enable();if(!1===e)return Ge&&Ge.kill();var t=e instanceof B?e:function(e){function t(){return u=!1}function n(){a=G(y,z),A=Ie(Je?1:0,a),g&&(C=Ie(0,G(y,X))),s=It}function r(){w._gsap.y=H(parseFloat(w._gsap.y)+_.offset)+"px",w.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(w._gsap.y)+", 0, 1)",_.offset=_.cacheID=0}function o(){n(),f.isActive()&&f.vars.scrollY>a&&(_()>a?f.progress(1)&&_(a):f.resetTo("scrollY",a))}Q(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var i,a,s,u,f,d,p,h,g=e.normalizeScrollX,v=e.momentum,m=e.allowNestedScroll,y=l(e.target)||Oe,b=Te.core.globals().ScrollSmoother,x=b&&b.get(),w=Je&&(e.content&&l(e.content)||x&&!1!==e.content&&!x.smooth()&&x.content()),_=c(y,z),S=c(y,X),k=1,E=(B.isTouch&&Me.visualViewport?Me.visualViewport.scale*Me.visualViewport.width:Me.outerWidth)/Me.innerWidth,T=0,P=$(v)?function(){return v(i)}:function(){return v||2.8},M=$t(y,e.type,!0,m),C=W,A=W;return w&&Te.set(w,{y:"+=0"}),e.ignoreCheck=function(e){return Je&&"touchmove"===e.type&&function(){if(u){requestAnimationFrame(t);var e=H(i.deltaY/2),n=A(_.v-e);if(w&&n!==_.v+_.offset){_.offset=n-_.v;var o=H((parseFloat(w&&w._gsap.y)||0)-_.offset);w.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+o+", 0, 1)",w._gsap.y=o+"px",_.cacheID=O.cache,Ft()}return!0}_.offset&&r(),u=!0}()||1.05<k&&"touchstart"!==e.type||i.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){var e=k;k=H((Me.visualViewport&&Me.visualViewport.scale||1)/E),f.pause(),e!==k&&Kt(y,1.01<k||!g&&"x"),d=S(),p=_(),n(),s=It},e.onRelease=e.onGestureStart=function(e,t){if(_.offset&&r(),t){O.cache++;var n,i,s=P();g&&(i=(n=S())+.05*s*-e.velocityX/.227,s*=Gt(S,n,i,G(y,X)),f.vars.scrollX=C(i)),i=(n=_())+.05*s*-e.velocityY/.227,s*=Gt(_,n,i,G(y,z)),f.vars.scrollY=A(i),f.invalidate().duration(s).play(.01),(Je&&f.vars.scrollY>=a||a-1<=n)&&Te.to({},{onUpdate:o,duration:s})}else h.restart(!0)},e.onWheel=function(){f._ts&&f.pause(),1e3<st()-T&&(s=0,T=st())},e.onChange=function(e,t,o,i,a){if(It!==s&&n(),t&&g&&S(C(i[2]===t?d+(e.startX-e.x):S()+t-i[1])),o){_.offset&&r();var l=a[2]===o,c=l?p+e.startY-e.y:_()+o-a[1],u=A(c);l&&c!==u&&(p+=u-c),_(u)}(o||t)&&Ft()},e.onEnable=function(){Kt(y,!g&&"x"),Ut.addEventListener("refresh",o),le(Me,"resize",o),_.smooth&&(_.target.style.scrollBehavior="auto",_.smooth=S.smooth=!1),M.enable()},e.onDisable=function(){Kt(y,!0),ce(Me,"resize",o),Ut.removeEventListener("refresh",o),M.kill()},e.lockAxis=!1!==e.lockAxis,((i=new B(e)).iOS=Je)&&!_()&&_(1),Je&&Te.ticker.add(W),h=i._dc,f=Te.to(i,{ease:"power4",paused:!0,scrollX:g?"+=0.1":"+=0",scrollY:"+=0.1",onComplete:h.vars.onComplete}),i}(e);return Ge&&Ge.target===t.target&&Ge.kill(),U(t.target)&&(Ge=t),t},Ut.core={_getVelocityProp:u,_inputObserver:$t,_scrollers:O,_proxies:A,bridge:{ss:function(){ct||Rt("scrollStart"),ct=st()},ref:function(){return Be}}},V()&&Te.registerPlugin(Ut),e.ScrollTrigger=Ut,e.default=Ut,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default}));