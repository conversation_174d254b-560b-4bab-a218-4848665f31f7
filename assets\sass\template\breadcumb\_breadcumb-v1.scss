.breadcumb-menu {
    max-width: 100%;
    padding: 0;
    margin: -0.4em 0 25px 0;
    list-style-type: none;
    position: relative;
    li {
        display: inline-block;
        margin-right: 3px;
        padding-right: 3px;
        list-style: none;
        position: relative;

        &:after {
            content: "\f105";
            position: relative;
            margin-left: 10px;
            font-weight: 500;
            font-size: 16px;
            color: $white-color;
            font-family: $icon-font;
        }

        &:last-child {
            padding-right: 0;
            margin-right: 0;
            color: $theme-color;

            &:after {
                display: none;
            }
        }
    }

    li,
    a,
    span {
        white-space: normal;
        word-break: break-word;
        font-weight: 500;
        font-size: 18px;
        font-family: $title-font;
        color: $white-color;
    }
    a:hover {
        color: $theme-color;
    }
}

.breadcumb-title {
    margin: -0.28em 0 -0.28em 0;
	font-size: 56px;
    line-height: 1.285;
    letter-spacing: 0.02em;
    color: $white-color;
}
body .th-header.header-default ~ .breadcumb-wrapper .breadcumb-content {
    padding: calc(var(--space) + 142px) 0 var(--space);
    @include md {
        padding: calc(var(--space) + 120px) 0 var(--space);
    }
    @include xs {
        padding: calc(var(--space) + 92px) 0 var(--space);
    }
}
body .th-header.header-default:has(.header-top) ~ .breadcumb-wrapper .breadcumb-content {
    padding: calc(var(--space) + 191px) 0 var(--space);
    @include md {
        padding: calc(var(--space) + 168px) 0 var(--space);
    }
    @include xs {
        padding: calc(var(--space) + 140px) 0 var(--space);
    }
}
.breadcumb-wrapper {
    background-color: $title-color;
    overflow: hidden;
    position: relative;
    z-index: 1;
    .breadcumb-content {
        --space: 148px;
        padding: var(--space) 0;
        position: relative;
        z-index: 1;
    }
    .breadcumb-thumb {
        position: relative; 
        z-index: 1;
        text-align: end;    
        margin: -124px -50px 0px 0;   
    }
}
@include lg {
    .breadcumb-wrapper {
        .breadcumb-content {
            --space: 120px;
        }
    }
	.breadcumb-menu {
        li,
        a,
        span {
            font-size: 16px;
        }
    }
    .breadcumb-title {
        font-size: 48px;
    }
}

@include md {
    .breadcumb-title {
        font-size: 46px;
    }
}
@include sm {
    .breadcumb-title {
        font-size: 40px;
    }
}
@include xs {
	.breadcumb-title {
        font-size: 34px;
	}
    .breadcumb-wrapper .breadcumb-content {
        --space: 100px;
    }
}

