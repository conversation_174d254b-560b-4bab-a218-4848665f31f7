/* Faq 1 ---------------------------------- */ 
.faq-wrap1 {
    background: $black-color2;
    padding: 32px;
}
.accordion-card {
    transition: 0.4s ease-in-out;
    border-radius: 0;
    overflow: hidden;
    background-color: $title-color;
    border: 1px solid $title-color;
    text-align: left;
    position: relative;
    z-index: 3;
    padding-bottom: 13px;
    &:not(:last-child) {
        margin-bottom: 16px;
    }
    &:has(.accordion-button.collapsed) {
        background-color: transparent;
        border: 1px solid rgba($color: #57585F, $alpha: 0.5);
    }
    .accordion-button {
        font-size: 24px;
        font-weight: 600;
        font-family: $title-font;
        border: 0;
        color: $white-color;
        background-color: transparent;
        // box-shadow: 0px 2px 14px rgba(4, 6, 66, 0.1);
        border-radius: 0;
        padding: 27px 55px 12px 30px;
        gap: 10px;
        margin-bottom: 0;
        text-align: left;
        transition: 0.3s;
        position: relative;
        &:after {
            content: "\2b";
            height: 100%;
            width: auto;
            line-height: 1;
            background-color: transparent;
            background-image: none;
            font-family: $icon-font;
            color: $white-color;
            font-weight: 500;
            font-size: 1em;
            display: grid;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: absolute;
            top: 6px;
            right: 30px;
            transition: 0.3s ease-in-out;
        }
        &:focus {
            outline: none;
            box-shadow: none;
        }
        &:not(.collapsed) {
            &:after {
                content: "\f00d";
                transform: rotate(90deg);
            }
        }
    }
    .accordion-collapse {
        border: none;
    }
    .accordion-body {
        border: none;
        padding: 0px 0 14px;
        margin: 0 30px;
    }
    .faq-text {
        margin-bottom: -0.48em;
        color: $light-color;
    }
}

@include xs {
    .accordion-card {
        padding-bottom: 6px;
        .accordion-button {
            font-size: 16px;
            padding: 20px 55px 12px 20px;
            &:after {
                right: 20px;
            }
        }
        .accordion-body {
            padding: 0px 0 20px;
            margin: 0 20px;
        }
        .faq-text {
            font-size: 14px;
        }
    }
}


/* Faq 2 ---------------------------------- */ 
.faq-img-box2 {
    .img1 {
        border-radius: 8px;
        overflow: hidden;
        img {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }
    }
}
.accordion-card.style2 {
    border-radius: 8px;
    .accordion-button {
        color: $title-color;
        transition: inherit;
        &:after {
            color: $title-color;
        }
        &:not(.collapsed) {
            color: $white-color;
            &:after {
                color: $white-color;
            }
        }
    }
    &:has(.accordion-collapse.show) {
        background-color: $title-color;
        border: 1px solid $title-color;
        .accordion-button {
            color: $white-color;
            &:after {
                color: $white-color;
            }
        }
    }
}

/* Faq 2 ---------------------------------- */ 
.accordion-card.style3 {
    border: 1px solid $light-color;
    border-radius: 16px;
    background: $white-color;
    padding-bottom: 23px;
    .accordion-button {
        color: $title-color;
        padding: 37px 70px 12px 48px;
        &:after {
            color: $title-color;
            top: 11px;
            right: 48px;
        }
    }
    .faq-text {
        color: $body-color;
    }
    .accordion-body {
        margin: 0 48px;
    }
    &:has(.accordion-collapse.show) {
        background-color: $white-color;
        border: 1px solid $light-color;
        box-shadow: 0px 30px 80px rgba(19, 24, 43, 0.2);
        .accordion-button {
            color: $title-color;
        }
    }
    &:has(.accordion-button.collapsed) {
        background-color: $white-color;
        border: 1px solid $light-color;
    }
    @include lg {
        padding-bottom: 13px;
        .accordion-button {
            padding: 27px 55px 12px 30px;
            font-size: 20px;
            &:after {
                right: 30px;
            }
        }
        .accordion-body {
            margin: 0 30px;
        }
    }
    @include md {
        border-radius: 8px;
    }
}

